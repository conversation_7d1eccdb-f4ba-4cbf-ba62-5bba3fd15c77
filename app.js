// 加载环境变量
require('dotenv').config();

const express = require('express');
const fetch = require('node-fetch');
const https = require('https');
const { logger } = require('./utils/logger');
const {
    moderationMiddleware,
    moderationStatsMiddleware,
    moderationWhitelistMiddleware,
    moderationRateLimitMiddleware
} = require('./middleware/moderationMiddleware');

// 创建HTTPS Agent，取消证书验证
const httpsAgent = new https.Agent({
    rejectUnauthorized: false, // 取消SSL证书强制验证
    keepAlive: true,
    timeout: 30000
});

const app = express();
app.use(express.json());

// 添加审核统计中间件
app.use(moderationStatsMiddleware());

// 添加审核速率限制中间件
// app.use(moderationRateLimitMiddleware({
//     maxRequests: 50,
//     windowMs: 60000 // 1分钟50次请求
// }));

// 上游服务配置
const UPSTREAM_CONFIG = {
    url: 'http://152.53.170.241:9512/v1/chat/completions'
};

/**
 * 解析流数据，提取Task ID和轮询URL - 更通用的匹配方式
 */
function parseStreamData(chunk) {
    const lines = chunk.toString().split('\n');
    let taskId = null;
    let pollingUrl = null;

    for (const line of lines) {
        if (line.startsWith('data: ')) {
            try {
                const data = JSON.parse(line.slice(6));
                if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
                    const content = data.choices[0].delta.content;

                    // 更通用的URL匹配 - 匹配 https://asyncdata.net/source/ 后面的内容
                    const urlMatch = content.match(/(https:\/\/asyncdata\.net\/source\/[^\s\)]+)/);
                    if (urlMatch) {
                        pollingUrl = urlMatch[1];

                        // 从URL中提取taskId - 取 /source/ 后面的部分
                        const taskIdFromUrl = pollingUrl.match(/\/source\/(.+)$/);
                        if (taskIdFromUrl) {
                            taskId = taskIdFromUrl[1];
                        }
                    }

                    // 如果从URL没有提取到taskId，尝试通用的taskId匹配
                    // 匹配被反引号包围的内容，且包含冒号的格式（如 veo2-fast:uuid）
                    if (!taskId) {
                        const taskIdMatch = content.match(/`([^`]*:[^`]+)`/);
                        if (taskIdMatch) {
                            taskId = taskIdMatch[1];
                        }
                    }
                }
            } catch (e) {
                // 忽略解析错误，继续处理下一行
            }
        }
    }

    return { taskId, pollingUrl };
}

/**
 * 从上游轮询URL获取最新状态
 * 支持多个数据源，优先从 godprivate.net，失败时使用 asyncdata.net
 * 包含重试机制处理网络问题和SSL错误
 */
async function fetchUpstreamStatus(taskId, maxRetries = 8, retryDelay = 1000) {
    // 定义多个数据源配置，按优先级排序
    const dataSources = [
        { name: 'godprivate.net', url: `http://godprivate.net:18081/source/${taskId}` },
        { name: 'asyncdata.net', url: `https://asyncdata.net/source/${taskId}` }
    ];

    let lastError;
    let currentSourceIndex = 0;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        // 选择当前尝试使用的数据源
        const currentSource = dataSources[currentSourceIndex];
        const { name: sourceName, url: currentUrl } = currentSource;

        try {
            logger.info(`[${attempt}/${maxRetries}] 尝试从 ${sourceName} 获取状态: ${currentUrl}`);

            const response = await fetch(currentUrl, {
                timeout: 30000,
                agent: currentUrl.startsWith('https:') ? httpsAgent : undefined
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            logger.info(`✅ 成功从 ${sourceName} 获取到状态数据`);
            return result;

        } catch (error) {
            lastError = error;
            logger.warn(`❌ [${attempt}/${maxRetries}] ${sourceName} 请求失败: ${error.message}`);

            // 检查是否是网络相关错误，包括SSL错误和更多网络问题
            const isNetworkError = error.message.includes('EAI_AGAIN') ||
                                 error.message.includes('ENOTFOUND') ||
                                 error.message.includes('ECONNRESET') ||
                                 error.message.includes('ETIMEDOUT') ||
                                 error.message.includes('EPROTO') ||
                                 error.message.includes('ECONNREFUSED') ||
                                 error.message.includes('EHOSTUNREACH') ||
                                 error.message.includes('ENETUNREACH') ||
                                 error.message.includes('EPIPE') ||
                                 error.message.includes('fetch failed') ||
                                 error.message.includes('timeout') ||
                                 error.message.includes('socket hang up') ||
                                 error.code === 'ENOTFOUND' ||
                                 error.code === 'ETIMEDOUT' ||
                                 error.code === 'ECONNRESET' ||
                                 error.code === 'ECONNREFUSED';

            // 检查是否是HTTP 5xx错误（服务器错误，可以重试）
            const isServerError = error.message.includes('HTTP 5') ||
                                 (error.response && error.response.status >= 500);

            // 如果还没达到最大重试次数，继续重试
            if (attempt < maxRetries) {
                // 对于网络错误或服务器错误，切换到下一个数据源
                if (isNetworkError || isServerError) {
                    const nextSourceIndex = (currentSourceIndex + 1) % dataSources.length;
                    const nextSourceName = dataSources[nextSourceIndex].name;

                    logger.info(`🔄 网络/服务器错误，切换数据源: ${sourceName} → ${nextSourceName}`);
                    currentSourceIndex = nextSourceIndex;

                    logger.warn(`⏳ ${retryDelay}ms 后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    // 指数退避：每次重试延迟时间翻倍，但最大不超过30秒
                    retryDelay = Math.min(retryDelay * 2, 30000);
                } else {
                    // 对于非网络错误（如404等），立即切换到下一个数据源
                    const nextSourceIndex = (currentSourceIndex + 1) % dataSources.length;
                    const nextSourceName = dataSources[nextSourceIndex].name;

                    logger.info(`🔄 非网络错误，立即切换数据源: ${sourceName} → ${nextSourceName}`);
                    currentSourceIndex = nextSourceIndex;
                }
                continue;
            }

            // 达到最大重试次数，跳出循环
            logger.error(`❌ 已达到最大重试次数 ${maxRetries}，停止尝试`);
            break;
        }
    }

    throw new Error(`Failed to fetch upstream status after ${maxRetries} attempts from all data sources. Last error from ${dataSources[currentSourceIndex].name}: ${lastError.message}`);
}

/**
 * Chat风格提交接口 - 获取到上游信息后立即返回
 */
app.post('/api/video/submit', moderationMiddleware(), async (req, res) => {
    try {
        const { prompt, model = 'veo2', images = [], ...otherParams } = req.body;

        if (!prompt) {
            return res.status(400).json({
                success: false,
                error: 'prompt is required'
            });
        }

        // 检查Authorization头
        const authorization = req.headers.authorization;
        if (!authorization) {
            return res.status(401).json({
                success: false,
                error: 'Authorization header is required'
            });
        }

        // 构建content，根据是否有图片决定格式
        let content;

        if (images.length > 0) {
            // 有图片时使用数组格式
            content = [
                {
                    type: "text",
                    text: prompt
                }
            ];

            // 添加image_url类型的content
            for (const imageUrl of images) {
                content.push({
                    type: "image_url",
                    image_url: {
                        url: imageUrl
                    }
                });
            }
        } else {
            // 没有图片时直接使用字符串
            content = prompt;
        }

        // 构建上游请求
        const upstreamRequest = {
            model,
            stream: true,
            messages: [
                { role: "user", content: content }
            ],
            ...otherParams
        };

        try {
            logger.info(`Starting chat task for prompt: ${prompt}`);
            if (images.length > 0) {
                logger.info(`With ${images.length} images: ${images.slice(0, 2).join(', ')}${images.length > 2 ? '...' : ''}`);
            }

            // 发起上游请求，使用客户端传来的Authorization
            const response = await fetch(UPSTREAM_CONFIG.url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authorization
                },
                body: JSON.stringify(upstreamRequest)
            });

            if (!response.ok) {
                // 尝试读取响应体以获取更多错误信息
                let errorBody = '';
                try {
                    errorBody = await response.text();
                } catch (e) {
                    errorBody = 'Unable to read response body';
                }

                const errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                const error = new Error(errorMessage);
                error.status = response.status;
                error.statusText = response.statusText;
                error.responseBody = errorBody;
                error.upstreamUrl = UPSTREAM_CONFIG.url;

                logger.error(`Upstream service error: ${errorMessage}`, {
                    status: response.status,
                    statusText: response.statusText,
                    responseBody: errorBody,
                    upstreamUrl: UPSTREAM_CONFIG.url,
                    requestBody: JSON.stringify(upstreamRequest, null, 2)
                });

                throw error;
            }

            let buffer = '';
            let upstreamTaskId = null;
            let pollingUrl = null;
            let responseResolved = false;



            // 监听流数据 - 获取到信息后忽略后续数据，让流自然结束
            const onData = chunk => {
                if (responseResolved) {
                    // 已经获取到需要的信息，忽略后续数据
                    logger.debug('Ignoring additional stream data after getting taskId');
                    return;
                }

                buffer += chunk.toString();

                // 尝试解析已接收的数据
                const { taskId: extractedTaskId, pollingUrl: extractedUrl } = parseStreamData(buffer);

                if (extractedTaskId && extractedUrl && !responseResolved) {
                    upstreamTaskId = extractedTaskId;
                    responseResolved = true;

                    logger.info(`✅ Got upstream task: ${upstreamTaskId}, ignoring further stream data`);

                    // 直接返回上游taskId，不做本地存储，不销毁流
                    res.json({
                        success: true,
                        data: {
                            taskId: upstreamTaskId,  // 直接使用上游taskId
                            pollingUrl: extractedUrl,
                            status: 'processing',
                            message: 'Task submitted successfully'
                        }
                    });

                    // 流会自然结束，我们不主动干预
                }
            };

            const onError = error => {
                if (!responseResolved) {
                    responseResolved = true;
                    logger.error(`Stream error: ${error.message}`, {
                        stack: error.stack,
                        name: error.name,
                        code: error.code
                    });
                    // 不主动销毁流，让其自然结束
                    res.status(500).json({
                        success: false,
                        error: `Stream error: ${error.message}`
                    });
                }
            };

            const onEnd = () => {
                if (!responseResolved) {
                    responseResolved = true;
                    logger.error(`Stream ended without getting task info`);
                    // 不主动销毁流，让其自然结束
                    res.status(500).json({
                        success: false,
                        error: 'Failed to extract task information from upstream response'
                    });
                }
            };

            // 简单直接的事件监听器绑定 - 获取信息后立即断开，不需要复杂的清理
            try {
                if (response.body && !response.body.destroyed) {
                    response.body.on('data', onData);
                    response.body.on('error', onError);
                    response.body.on('end', onEnd);
                    logger.debug('✅ Stream listeners attached, will disconnect after getting taskId');
                } else {
                    throw new Error('Response body is unavailable or destroyed');
                }
            } catch (listenerError) {
                logger.error(`❌ Failed to attach stream listeners: ${listenerError.message}`);
                responseResolved = true;
                res.status(500).json({
                    success: false,
                    error: `Failed to setup stream listeners: ${listenerError.message}`
                });
                return;
            }

            // 设置超时，防止无限等待
            const timeoutId = setTimeout(() => {
                if (!responseResolved) {
                    responseResolved = true;
                    logger.warn(`Stream timeout after 30s for submit request`);
                    // 不主动销毁流，让其自然结束
                    res.status(500).json({
                        success: false,
                        error: 'Timeout waiting for upstream task information'
                    });
                }
            }, 30000); // 30秒超时

            // 确保在响应完成后清理超时
            res.on('finish', () => {
                clearTimeout(timeoutId);
            });

        } catch (error) {
            // 构建详细的错误信息
            const errorDetails = {
                stack: error.stack,
                name: error.name,
                code: error.code
            };

            // 如果是上游服务错误，添加更多信息
            if (error.status) {
                errorDetails.upstreamStatus = error.status;
                errorDetails.upstreamStatusText = error.statusText;
                errorDetails.upstreamUrl = error.upstreamUrl;
                errorDetails.upstreamResponse = error.responseBody;
            }

            logger.error(`Request error: ${error.message}`, errorDetails);

            // 根据错误类型返回不同的状态码和错误信息
            let statusCode = 500;
            let errorMessage = `Request error: ${error.message}`;

            if (error.status) {
                // 上游服务错误
                statusCode = error.status === 401 ? 401 : 502; // 401保持原样，其他错误返回502 Bad Gateway
                errorMessage = `Upstream service error: ${error.message}`;

                if (error.responseBody && error.responseBody.length < 500) {
                    errorMessage += ` - ${error.responseBody}`;
                }
            }

            res.status(statusCode).json({
                success: false,
                error: errorMessage,
                details: {
                    type: 'UPSTREAM_ERROR',
                    upstreamStatus: error.status,
                    timestamp: new Date().toISOString()
                }
            });
        }

    } catch (error) {
        logger.error(`Submit error: ${error.message}`, {
            stack: error.stack,
            name: error.name,
            code: error.code
        });
        res.status(500).json({
            success: false,
            error: `Submit task failed: ${error.message}`,
            details: {
                type: 'SUBMIT_ERROR',
                name: error.name,
                code: error.code
            }
        });
    }
});

/**
 * Chat风格查询接口 - 直接请求上游
 */
app.get('/api/video/status/:taskId', async (req, res) => {
    try {
        const { taskId } = req.params;

        try {
            logger.info(`Querying upstream status for: ${taskId}`);
            const upstreamStatus = await fetchUpstreamStatus(taskId);

            // 构建响应
            const response = {
                success: true,
                data: {
                    taskId,
                    upstreamData: upstreamStatus
                }
            };

            // 根据上游状态判断任务状态
            if (upstreamStatus.video_url) {
                response.data.status = 'completed';
                response.data.result = {
                    video_url: upstreamStatus.video_url,
                    video_media_id: upstreamStatus.video_media_id
                };
            } else if (upstreamStatus.status === 'failed' || upstreamStatus.retry_count > upstreamStatus.max_retries) {
                response.data.status = 'failed';
                response.data.error = 'Video generation failed';
            } else {
                response.data.status = 'processing';
                response.data.progress = {
                    upstreamStatus: upstreamStatus.status,
                    videoGenerationStatus: upstreamStatus.video_generation_status,
                    upsampling: upstreamStatus.upsample_status || null,
                    retryCount: upstreamStatus.retry_count,
                    maxRetries: upstreamStatus.max_retries
                };
            }

            res.json(response);

        } catch (error) {
            logger.error(`Failed to fetch status for ${taskId}: ${error.message}`, {
                stack: error.stack,
                name: error.name,
                code: error.code
            });
            res.status(500).json({
                success: false,
                error: `Failed to fetch task status: ${error.message}`
            });
        }

    } catch (error) {
        logger.error(`Status query error: ${error.message}`, {
            stack: error.stack,
            name: error.name,
            code: error.code
        });
        res.status(500).json({
            success: false,
            error: `Status query failed: ${error.message}`,
            details: {
                type: 'STATUS_QUERY_ERROR',
                name: error.name,
                code: error.code
            }
        });
    }
});

/**
 * 标准视频创建接口 - 接收视频参数并转换为chat格式
 */
app.post('/v1/video/create', moderationMiddleware(), async (req, res) => {
    try {
        const { prompt, model, images = [], enhance_prompt = true } = req.body;

        // 参数验证
        if (!prompt) {
            return res.status(400).json({
                success: false,
                error: 'prompt is required'
            });
        }

        if (!model) {
            return res.status(400).json({
                success: false,
                error: 'model is required'
            });
        }

        // 验证images参数（可选）
        if (!Array.isArray(images)) {
            return res.status(400).json({
                success: false,
                error: 'images must be an array'
            });
        }

        if (images.length > 3) {
            return res.status(400).json({
                success: false,
                error: 'images array cannot contain more than 3 images'
            });
        }

        // 检查Authorization头
        const authorization = req.headers.authorization;
        if (!authorization) {
            return res.status(401).json({
                success: false,
                error: 'Authorization header is required'
            });
        }

        // 构建content，根据是否有图片决定格式
        let content;

        if (images.length > 0) {
            // 有图片时使用数组格式
            content = [
                {
                    type: "text",
                    text: prompt
                }
            ];

            // 添加image_url类型的content
            for (const imageUrl of images) {
                content.push({
                    type: "image_url",
                    image_url: {
                        url: imageUrl
                    }
                });
            }
        } else {
            // 没有图片时直接使用字符串
            content = prompt;
        }

        // 构建上游请求（chat格式）
        const upstreamRequest = {
            model,
            stream: true,
            messages: [
                { role: "user", content: content }
            ],
            // 添加原始参数作为额外参数
            enhance_prompt
        };

        try {
            logger.info(`Starting video creation`);
            logger.info(`Prompt: ${prompt}`);
            logger.info(`Model: ${model}`);
            logger.info(`Images: ${images.length} items`);
            if (images.length > 0) {
                logger.info(`Image URLs: ${images.slice(0, 2).join(', ')}${images.length > 2 ? '...' : ''}`);
            }

            // 发起上游请求，使用客户端传来的Authorization
            const response = await fetch(UPSTREAM_CONFIG.url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authorization
                },
                body: JSON.stringify(upstreamRequest)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            let buffer = '';
            let upstreamTaskId = null;
            let pollingUrl = null;
            let enhancedPrompt = null;
            let responseResolved = false;



            // 监听流数据 - 获取到信息后忽略后续数据，让流自然结束
            const onData = chunk => {
                if (responseResolved) {
                    // 已经获取到需要的信息，忽略后续数据
                    logger.debug('Ignoring additional video stream data after getting taskId');
                    return;
                }

                buffer += chunk.toString();

                // 尝试解析已接收的数据
                const { taskId: extractedTaskId, pollingUrl: extractedUrl } = parseStreamData(buffer);

                // 尝试提取enhanced_prompt
                if (!enhancedPrompt) {
                    const enhancedMatch = buffer.match(/enhanced[_\s]prompt[:\s]*["']([^"']+)["']/i);
                    if (enhancedMatch) {
                        enhancedPrompt = enhancedMatch[1];
                    }
                }

                if (extractedTaskId && extractedUrl && !responseResolved) {
                    upstreamTaskId = extractedTaskId;
                    responseResolved = true;

                    logger.info(`✅ Got upstream video task: ${upstreamTaskId}, ignoring further stream data`);

                    // 返回符合API规范的结果，不销毁流
                    const responseData = {
                        id: upstreamTaskId,  // 直接使用上游taskId
                        status: 'pending',
                        status_update_time: Date.now()
                    };

                    // 如果有enhanced_prompt则包含
                    if (enhancedPrompt) {
                        responseData.enhanced_prompt = enhancedPrompt;
                    }

                    res.json(responseData);

                    // 流会自然结束，我们不主动干预
                }
            };

            const onError = error => {
                if (!responseResolved) {
                    responseResolved = true;
                    logger.error(`Stream error for video task: ${error.message}`, {
                        stack: error.stack,
                        name: error.name,
                        code: error.code
                    });
                    // 不主动销毁流，让其自然结束
                    res.status(500).json({
                        success: false,
                        error: `Stream error: ${error.message}`
                    });
                }
            };

            const onEnd = () => {
                if (!responseResolved) {
                    responseResolved = true;
                    logger.error(`Stream ended without getting task info for video task`);
                    // 不主动销毁流，让其自然结束
                    res.status(500).json({
                        success: false,
                        error: 'Failed to extract task information from upstream response'
                    });
                }
            };

            // 添加事件监听器，并捕获可能的绑定错误
            try {
                // 检查流对象状态
                if (!response.body) {
                    throw new Error('Response body is null or undefined');
                }

                if (response.body.destroyed) {
                    throw new Error('Response body stream is already destroyed');
                }

                // 检查是否已经有监听器绑定
                const existingListeners = response.body.listenerCount('data') +
                                        response.body.listenerCount('error') +
                                        response.body.listenerCount('end');

                if (existingListeners > 0) {
                    logger.warn(`⚠️  Video stream already has ${existingListeners} listeners, cleaning up first`);
                    response.body.removeAllListeners('data');
                    response.body.removeAllListeners('error');
                    response.body.removeAllListeners('end');
                }

                // 使用 setImmediate 确保在下一个事件循环中绑定监听器
                setImmediate(() => {
                    try {
                        if (!responseResolved && response.body && !response.body.destroyed) {
                            response.body.on('data', onData);
                            response.body.on('error', onError);
                            response.body.on('end', onEnd);
                            logger.debug('✅ Video stream event listeners attached successfully');
                        } else {
                            logger.warn('⚠️  Skipped video listener attachment - stream unavailable or response resolved');
                        }
                    } catch (deferredError) {
                        logger.error(`❌ Deferred video listener attachment failed: ${deferredError.message}`, {
                            stack: deferredError.stack,
                            name: deferredError.name,
                            code: deferredError.code,
                            streamDestroyed: response.body ? response.body.destroyed : 'no body',
                            responseResolved
                        });

                        if (!responseResolved) {
                            responseResolved = true;
                            res.status(500).json({
                                success: false,
                                error: `Failed to setup deferred video stream listeners: ${deferredError.message}`
                            });
                        }
                    }
                });

            } catch (listenerError) {
                logger.error(`❌ Failed to attach video stream listeners: ${listenerError.message}`, {
                    stack: listenerError.stack,
                    name: listenerError.name,
                    code: listenerError.code,
                    streamDestroyed: response.body ? response.body.destroyed : 'no body',
                    streamReadable: response.body ? response.body.readable : 'no body',
                    existingListeners: response.body ? {
                        data: response.body.listenerCount('data'),
                        error: response.body.listenerCount('error'),
                        end: response.body.listenerCount('end')
                    } : 'no body'
                });
                responseResolved = true;
                res.status(500).json({
                    success: false,
                    error: `Failed to setup video stream listeners: ${listenerError.message}`
                });
                return;
            }

            // 设置超时，防止无限等待
            const timeoutId = setTimeout(() => {
                if (!responseResolved) {
                    responseResolved = true;
                    logger.warn(`Stream timeout after 30s for video task`);
                    // 不主动销毁流，让其自然结束
                    res.status(500).json({
                        success: false,
                        error: 'Timeout waiting for upstream task information'
                    });
                }
            }, 30000); // 30秒超时

            // 确保在响应完成后清理超时
            res.on('finish', () => {
                clearTimeout(timeoutId);
            });

        } catch (error) {
            logger.error(`Request error for video task: ${error.message}`, {
                stack: error.stack,
                name: error.name,
                code: error.code
            });
            res.status(500).json({
                success: false,
                error: `Request error: ${error.message}`
            });
        }

    } catch (error) {
        logger.error(`Video creation error: ${error.message}`, {
            stack: error.stack,
            name: error.name,
            code: error.code
        });
        res.status(500).json({
            success: false,
            error: `Video creation failed: ${error.message}`,
            details: {
                type: 'VIDEO_CREATION_ERROR',
                name: error.name,
                code: error.code
            }
        });
    }
});

/**
 * 查询视频任务状态 - 符合API规范的响应格式
 */
app.get('/v1/video/status/:taskId', async (req, res) => {
    try {
        const { taskId } = req.params;

        try {
            logger.info(`Querying video status for: ${taskId}`);
            const upstreamStatus = await fetchUpstreamStatus(taskId);

            // 将上游状态映射到API规范的状态
            let apiStatus = 'pending';
            if (upstreamStatus.video_url) {
                apiStatus = 'completed';
            } else if (upstreamStatus.status === 'failed' || upstreamStatus.retry_count >= upstreamStatus.max_retries) {
                apiStatus = 'failed';
            } else {
                // 根据上游状态映射
                switch (upstreamStatus.status) {
                    case 'video_generating':
                        apiStatus = 'video_generating';
                        break;
                    case 'video_upsampling':
                        apiStatus = 'video_upsampling';
                        break;
                    default:
                        apiStatus = 'pending';
                }
            }

            // 构建符合API规范的响应
            const response = {
                id: taskId,
                status: apiStatus,
                status_update_time: upstreamStatus.status_update_time || Date.now()
            };

            // 添加可选字段
            if (upstreamStatus.video_url) {
                response.video_url = upstreamStatus.video_url;
            }

            if (upstreamStatus.video_media_id) {
                response.video_media_id = upstreamStatus.video_media_id;
            }

            res.json(response);

        } catch (error) {
            logger.error(`Failed to fetch video status for ${taskId}: ${error.message}`, {
                stack: error.stack,
                name: error.name,
                code: error.code
            });
            res.status(404).json({
                success: false,
                error: `Task not found or upstream error: ${error.message}`
            });
        }

    } catch (error) {
        logger.error(`Video status query error: ${error.message}`, {
            stack: error.stack,
            name: error.name,
            code: error.code
        });
        res.status(500).json({
            success: false,
            error: `Video status query failed: ${error.message}`,
            details: {
                type: 'VIDEO_STATUS_QUERY_ERROR',
                name: error.name,
                code: error.code
            }
        });
    }
});

/**
 * 审核统计接口
 */
app.get('/api/moderation/stats', (req, res) => {
    const moderationService = require('./services/moderationService');
    const stats = moderationService.getStats();

    res.json({
        success: true,
        data: {
            ...stats,
            config: {
                enabled: require('./config/moderation').MODERATION_CONFIG.enabled,
                strictness: require('./config/moderation').MODERATION_CONFIG.strictness,
                textModeration: require('./config/moderation').MODERATION_CONFIG.text.enabled,
                imageModeration: require('./config/moderation').MODERATION_CONFIG.image.enabled,
                cacheEnabled: require('./config/moderation').MODERATION_CONFIG.cache.enabled
            }
        },
        timestamp: new Date().toISOString()
    });
});

/**
 * 内容审核测试接口
 */
app.post('/api/moderation/test', async (req, res) => {
    try {
        const { text, imageUrl } = req.body;
        const moderationService = require('./services/moderationService');

        const results = [];

        // 审核文字内容
        if (text) {
            const textResult = await moderationService.moderateText(text);
            results.push({
                type: 'text',
                content: text,
                ...textResult
            });
        }

        // 审核图片内容
        if (imageUrl) {
            const imageResult = await moderationService.moderateImage(imageUrl);
            results.push({
                type: 'image',
                content: imageUrl,
                ...imageResult
            });
        }

        // 汇总审核结果
        const finalResult = moderationService.aggregateResults(results);

        res.json({
            success: true,
            data: finalResult,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Moderation test error:', error);
        res.status(500).json({
            success: false,
            error: 'Moderation test failed',
            message: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

/**
 * 清理审核缓存接口
 */
app.post('/api/moderation/clear-cache', (req, res) => {
    const moderationService = require('./services/moderationService');
    moderationService.clearCache();

    res.json({
        success: true,
        message: 'Moderation cache cleared',
        timestamp: new Date().toISOString()
    });
});

/**
 * 获取审核规则接口
 */
app.get('/api/moderation/rules', (req, res) => {
    try {
        const moderationRulesService = require('./services/moderationRulesService');
        const rules = moderationRulesService.getAllRules();

        res.json({
            success: true,
            data: rules,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Get rules error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get moderation rules',
            message: error.message
        });
    }
});

/**
 * 添加敏感词接口
 */
app.post('/api/moderation/sensitive-words', async (req, res) => {
    try {
        const { category, words } = req.body;

        if (!category || !words || !Array.isArray(words)) {
            return res.status(400).json({
                success: false,
                error: 'Category and words array are required'
            });
        }

        const moderationRulesService = require('./services/moderationRulesService');
        const result = await moderationRulesService.addSensitiveWords(category, words);

        res.json({
            success: true,
            data: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Add sensitive words error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to add sensitive words',
            message: error.message
        });
    }
});

/**
 * 删除敏感词接口
 */
app.delete('/api/moderation/sensitive-words', async (req, res) => {
    try {
        const { category, words } = req.body;

        if (!category || !words || !Array.isArray(words)) {
            return res.status(400).json({
                success: false,
                error: 'Category and words array are required'
            });
        }

        const moderationRulesService = require('./services/moderationRulesService');
        const result = await moderationRulesService.removeSensitiveWords(category, words);

        res.json({
            success: true,
            data: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Remove sensitive words error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to remove sensitive words',
            message: error.message
        });
    }
});

/**
 * 健康检查接口
 */
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        message: 'Video proxy service is running',
        uptime: process.uptime()
    });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    logger.error(`Unhandled error: ${error.message}`, {
        stack: error.stack,
        name: error.name,
        code: error.code
    });
    res.status(500).json({
        success: false,
        error: `Unhandled server error: ${error.message}`,
        details: {
            type: 'UNHANDLED_ERROR',
            name: error.name,
            code: error.code,
            timestamp: new Date().toISOString()
        }
    });
});

const PORT = process.env.PORT || 8847;
app.listen(PORT, () => {
    logger.info(`Video generation proxy server running on port ${PORT}`);
    logger.info(`Health check: http://localhost:${PORT}/health`);
    logger.info(`\n=== Chat-style Interface ===`);
    logger.info(`Submit task: POST http://localhost:${PORT}/api/video/submit`);
    logger.info(`Query status: GET http://localhost:${PORT}/api/video/status/{taskId}`);
    logger.info(`\n=== Standard Video API Interface ===`);
    logger.info(`Create video: POST http://localhost:${PORT}/v1/video/create`);
    logger.info(`Query video: GET http://localhost:${PORT}/v1/video/status/{taskId}`);
    logger.info(`\n=== Content Moderation Interface ===`);
    logger.info(`Test content: POST http://localhost:${PORT}/api/moderation/test`);
    logger.info(`View stats: GET http://localhost:${PORT}/api/moderation/stats`);
    logger.info(`Manage rules: GET http://localhost:${PORT}/api/moderation/rules`);
    logger.info(`Clear cache: POST http://localhost:${PORT}/api/moderation/clear-cache`);

    // 显示审核配置状态
    const { MODERATION_CONFIG } = require('./config/moderation');
    logger.info(`\n=== Moderation Status ===`);
    logger.info(`Enabled: ${MODERATION_CONFIG.enabled}`);
    logger.info(`Strictness: ${MODERATION_CONFIG.strictness}`);
    logger.info(`Text moderation: ${MODERATION_CONFIG.text.enabled}`);
    logger.info(`Image moderation: ${MODERATION_CONFIG.image.enabled}`);
    logger.info(`Cache enabled: ${MODERATION_CONFIG.cache.enabled}`);
});

// 全局错误处理 - 捕获未处理的Promise拒绝

process.on('unhandledRejection', (reason, promise) => {
    logger.error('🚨 Unhandled Rejection (服务继续运行) at:', promise);
    logger.error('Reason:', reason);

    // 如果reason是Error对象，记录更多详情
    if (reason instanceof Error) {
        logger.error('Error stack:', reason.stack);
        logger.error('Error details:', {
            name: reason.name,
            message: reason.message,
            code: reason.code,
            timestamp: new Date().toISOString(),
            pid: process.pid
        });
    }

    // 记录错误但不退出进程
    logger.warn('⚠️  检测到未处理的Promise拒绝，已记录但服务继续运行');
});

// 监听进程警告（包括事件监听器相关的警告）
process.on('warning', (warning) => {
    logger.warn('🔔 Process Warning:', {
        name: warning.name,
        message: warning.message,
        stack: warning.stack
    });

    // 特别关注事件循环相关的警告
    if (warning.message && warning.message.includes('event loop')) {
        logger.error('🚨 Event Loop Warning Detected:', {
            name: warning.name,
            message: warning.message,
            stack: warning.stack,
            timestamp: new Date().toISOString(),
            pid: process.pid,
            memory: process.memoryUsage()
        });
    }
});

// 添加专门的事件循环错误捕获
process.on('uncaughtException', (error) => {
    // 检查是否是事件循环绑定错误
    if (error.message && error.message.includes('bound to a different event loop')) {
        logger.error('🚨 EVENT LOOP BINDING ERROR DETECTED:', {
            message: error.message,
            stack: error.stack,
            name: error.name,
            code: error.code,
            timestamp: new Date().toISOString(),
            pid: process.pid,
            memory: process.memoryUsage(),
            activeHandles: process._getActiveHandles ? process._getActiveHandles().length : 'unknown',
            activeRequests: process._getActiveRequests ? process._getActiveRequests().length : 'unknown'
        });

        // 记录当前活跃的流和监听器
        logger.error('🔍 Process State at Error:', {
            uptime: process.uptime(),
            version: process.version,
            platform: process.platform,
            arch: process.arch
        });
    }

    logger.error(`🚨 Uncaught Exception (服务继续运行): ${error.message}`);
    logger.error('Stack trace:', error.stack);

    // 记录详细的错误信息
    logger.error('Error details:', {
        name: error.name,
        message: error.message,
        code: error.code,
        errno: error.errno,
        syscall: error.syscall,
        address: error.address,
        port: error.port,
        timestamp: new Date().toISOString(),
        pid: process.pid,
        memory: process.memoryUsage()
    });

    // 记录错误但不退出进程，让服务继续运行
    logger.warn('⚠️  检测到未捕获异常，已记录但服务继续运行');
});

logger.info('✅ Global error handlers registered');

module.exports = app;
