const moderationService = require('../services/moderationService');
const { MODERATION_CONFIG, MODERATION_MESSAGES, detectLanguage } = require('../config/moderation');
const { logger } = require('../utils/logger');

/**
 * 内容审核中间件
 */
function moderationMiddleware(options = {}) {
    return async (req, res, next) => {
        // 如果审核被禁用，直接通过
        if (!MODERATION_CONFIG.enabled) {
            return next();
        }

        // 记录审核开始时间
        const startTime = Date.now();
        
        try {
            logger.info(`[Moderation] Starting content moderation for ${req.method} ${req.path}`);

            // 提取需要审核的内容
            const contentToModerate = extractContentFromRequest(req);

            if (!contentToModerate) {
                logger.info(`[Moderation] No content to moderate, skipping`);
                return next();
            }

            // 检测语言
            let detectedLanguage = 'en';
            let allTextContent = '';

            if (contentToModerate.messages) {
                for (const message of contentToModerate.messages) {
                    if (message.content) {
                        if (typeof message.content === 'string') {
                            allTextContent += message.content + ' ';
                        } else if (Array.isArray(message.content)) {
                            for (const item of message.content) {
                                if (item.type === 'text' && item.text) {
                                    allTextContent += item.text + ' ';
                                }
                            }
                        }
                    }
                }
            }

            if (allTextContent.trim()) {
                detectedLanguage = detectLanguage(allTextContent);
            }

            // 执行内容审核
            const moderationResult = await moderationService.moderateRequest(contentToModerate);
            
            // 记录审核耗时
            const duration = Date.now() - startTime;
            logger.info(`[Moderation] Completed in ${duration}ms, result: ${moderationResult.approved ? 'APPROVED' : 'REJECTED'}`);

            // 如果审核通过，继续处理请求
            if (moderationResult.approved) {
                // 在请求对象中添加审核信息
                req.moderationResult = moderationResult;
                return next();
            }

            // 审核不通过，返回错误响应
            logger.warn(`[Moderation] Content rejected:`, moderationResult);

            const messages = MODERATION_MESSAGES[detectedLanguage] || MODERATION_MESSAGES.en;
            const defaultSuggestions = detectedLanguage === 'zh'
                ? '请修改内容以符合平台规范'
                : 'Please modify content to comply with platform standards';

            return res.status(400).json({
                success: false,
                error: messages.contentModerationFailed,
                code: 'CONTENT_MODERATION_FAILED',
                details: {
                    reason: moderationResult.reason,
                    violations: moderationResult.violations || [],
                    suggestions: moderationResult.suggestions || defaultSuggestions,
                    rejectedCount: moderationResult.rejectedCount,
                    totalCount: moderationResult.totalCount
                },
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            const duration = Date.now() - startTime;
            logger.error(`[Moderation] Error after ${duration}ms:`, error);

            // 审核服务出错时的处理策略
            const messages = MODERATION_MESSAGES[detectedLanguage] || MODERATION_MESSAGES.en;

            if (options.failOpen !== false) {
                // 默认策略：审核失败时允许请求通过，但记录错误
                logger.warn(`[Moderation] Allowing request due to moderation service error`);
                req.moderationResult = {
                    approved: true,
                    reason: 'Moderation service error - fail open',
                    error: error.message
                };
                return next();
            } else {
                // 严格策略：审核失败时拒绝请求
                return res.status(500).json({
                    success: false,
                    error: messages.moderationServiceError,
                    code: 'MODERATION_SERVICE_ERROR',
                    details: {
                        reason: messages.unableToVerifyContent,
                        suggestions: messages.pleaseRetryLater
                    },
                    timestamp: new Date().toISOString()
                });
            }
        }
    };
}

/**
 * 从请求中提取需要审核的内容
 */
function extractContentFromRequest(req) {
    const body = req.body;
    
    // 检查不同的请求格式
    
    // 1. Chat格式 (messages数组)
    if (body.messages && Array.isArray(body.messages)) {
        return {
            messages: body.messages,
            model: body.model
        };
    }
    
    // 2. 简单格式 (prompt + images)
    if (body.prompt) {
        const content = [];
        
        // 添加文字内容
        content.push({
            type: 'text',
            text: body.prompt
        });
        
        // 添加图片内容
        if (body.images && Array.isArray(body.images)) {
            body.images.forEach(imageUrl => {
                content.push({
                    type: 'image_url',
                    image_url: { url: imageUrl }
                });
            });
        }
        
        return {
            messages: [{
                role: 'user',
                content: content
            }],
            model: body.model
        };
    }
    
    // 3. 其他格式的内容提取
    if (body.content) {
        return {
            messages: [{
                role: 'user',
                content: body.content
            }]
        };
    }
    
    return null;
}

/**
 * 审核统计中间件
 * 注意：移除了响应头设置功能，避免HTTP头字符限制问题
 * 审核信息已通过日志记录，统计信息可通过API获取
 */
function moderationStatsMiddleware() {
    return (req, res, next) => {
        // 简化版本：只是一个占位符中间件
        // 实际的统计信息收集在moderationService中进行
        next();
    };
}

/**
 * 审核白名单中间件
 */
function moderationWhitelistMiddleware(whitelist = []) {
    return (req, res, next) => {
        // 检查IP白名单
        const clientIP = req.ip || req.connection.remoteAddress;
        if (whitelist.includes(clientIP)) {
            logger.info(`[Moderation] IP ${clientIP} is whitelisted, skipping moderation`);
            req.moderationResult = {
                approved: true,
                reason: 'IP whitelisted'
            };
            return next();
        }

        // 检查API Key白名单
        const apiKey = req.headers.authorization;
        if (apiKey && whitelist.includes(apiKey)) {
            logger.info(`[Moderation] API key is whitelisted, skipping moderation`);
            req.moderationResult = {
                approved: true,
                reason: 'API key whitelisted'
            };
            return next();
        }
        
        next();
    };
}

/**
 * 审核速率限制中间件
 */
function moderationRateLimitMiddleware(options = {}) {
    const requests = new Map();
    const maxRequests = options.maxRequests || 100;
    const windowMs = options.windowMs || 60000; // 1分钟

    return (req, res, next) => {
        // 如果审核被禁用，直接通过，不进行速率限制检查
        if (!MODERATION_CONFIG.enabled) {
            return next();
        }

        const key = req.ip || req.connection.remoteAddress;
        const now = Date.now();

        // 清理过期记录
        if (requests.has(key)) {
            const userRequests = requests.get(key);
            const validRequests = userRequests.filter(time => now - time < windowMs);
            requests.set(key, validRequests);
        }

        // 检查请求频率
        const userRequests = requests.get(key) || [];
        if (userRequests.length >= maxRequests) {
            return res.status(429).json({
                success: false,
                error: 'Too many moderation requests',
                code: 'MODERATION_RATE_LIMIT',
                details: {
                    reason: `Maximum ${maxRequests} requests per ${windowMs/1000} seconds`,
                    retryAfter: Math.ceil((userRequests[0] + windowMs - now) / 1000)
                }
            });
        }

        // 记录请求
        userRequests.push(now);
        requests.set(key, userRequests);

        next();
    };
}

module.exports = {
    moderationMiddleware,
    moderationStatsMiddleware,
    moderationWhitelistMiddleware,
    moderationRateLimitMiddleware
};
