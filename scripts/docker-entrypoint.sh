#!/bin/sh

# Docker 容器启动脚本
# 确保日志目录权限正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting video-proxy server...${NC}"

# 检查并创建日志目录
LOG_DIR="${LOG_DIR:-/app/logs}"
echo -e "${YELLOW}📁 Checking log directory: $LOG_DIR${NC}"

if [ ! -d "$LOG_DIR" ]; then
    echo -e "${YELLOW}📁 Creating log directory...${NC}"
    mkdir -p "$LOG_DIR"
fi

# 显示当前权限信息
echo -e "${YELLOW}📋 Current directory permissions:${NC}"
ls -la "$LOG_DIR" 2>/dev/null || echo "  Directory does not exist or is not accessible"
ls -ld "$LOG_DIR" 2>/dev/null || echo "  Cannot check directory permissions"

# 尝试修复权限（如果是root用户运行启动脚本）
if [ "$(id -u)" = "0" ]; then
    echo -e "${YELLOW}🔧 Running as root, attempting to fix permissions...${NC}"
    chown -R nodejs:nodejs "$LOG_DIR" 2>/dev/null || echo -e "${RED}❌ Failed to change ownership${NC}"
    chmod -R 755 "$LOG_DIR" 2>/dev/null || echo -e "${RED}❌ Failed to change permissions${NC}"
fi

# 检查写入权限
if [ -w "$LOG_DIR" ]; then
    echo -e "${GREEN}✅ Log directory is writable${NC}"

    # 创建测试文件来验证权限
    TEST_FILE="$LOG_DIR/.write_test"
    if echo "test" > "$TEST_FILE" 2>/dev/null; then
        rm -f "$TEST_FILE"
        echo -e "${GREEN}✅ Write permission verified${NC}"
    else
        echo -e "${RED}❌ Cannot write to log directory${NC}"
        echo -e "${YELLOW}⚠️  File logging will be disabled${NC}"
    fi
else
    echo -e "${RED}❌ Log directory is not writable${NC}"
    echo -e "${YELLOW}⚠️  File logging will be disabled${NC}"

    # 尝试创建一个用户可写的子目录
    USER_LOG_DIR="$LOG_DIR/user"
    echo -e "${YELLOW}🔄 Trying to create user log directory: $USER_LOG_DIR${NC}"
    if mkdir -p "$USER_LOG_DIR" 2>/dev/null; then
        echo -e "${GREEN}✅ Created user log directory${NC}"
        export LOG_DIR="$USER_LOG_DIR"
    else
        echo -e "${RED}❌ Cannot create user log directory${NC}"
    fi
fi

# 显示环境信息
echo -e "${YELLOW}📋 Environment configuration:${NC}"
echo "  NODE_ENV: ${NODE_ENV:-development}"
echo "  LOG_LEVEL: ${LOG_LEVEL:-info}"
echo "  LOG_TO_FILE: ${LOG_TO_FILE:-true}"
echo "  LOG_DIR: $LOG_DIR"
echo "  LOG_MAX_SIZE: ${LOG_MAX_SIZE:-50MB}"
echo "  LOG_MAX_FILES: ${LOG_MAX_FILES:-10}"

# 显示用户信息
echo -e "${YELLOW}👤 Current user: $(whoami) ($(id))${NC}"

# 如果当前是root用户，切换到nodejs用户执行应用
if [ "$(id -u)" = "0" ]; then
    echo -e "${YELLOW}🔄 Switching to nodejs user...${NC}"
    echo -e "${GREEN}🎯 Starting application as nodejs user...${NC}"
    echo ""

    # 使用gosu或su切换用户执行命令
    if command -v gosu >/dev/null 2>&1; then
        exec gosu nodejs "$@"
    else
        exec su-exec nodejs "$@"
    fi
else
    echo -e "${GREEN}🎯 Starting application...${NC}"
    echo ""

    # 直接执行传入的命令
    exec "$@"
fi
