#!/bin/bash

# 日志管理脚本
# 用于管理应用程序日志文件

LOG_DIR="./logs"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "日志管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  tail       实时查看主日志文件"
    echo "  tail-error 实时查看错误日志文件"
    echo "  show       显示最近的日志"
    echo "  show-error 显示最近的错误日志"
    echo "  clean      清理旧的日志文件"
    echo "  size       显示日志文件大小"
    echo "  rotate     手动轮转日志文件"
    echo "  help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 tail              # 实时查看日志"
    echo "  $0 show 50           # 显示最近50行日志"
    echo "  $0 clean 7           # 清理7天前的日志文件"
}

# 检查日志目录
check_log_dir() {
    if [ ! -d "$LOG_DIR" ]; then
        echo -e "${YELLOW}警告: 日志目录不存在，正在创建...${NC}"
        mkdir -p "$LOG_DIR"
    fi
}

# 实时查看主日志
tail_log() {
    check_log_dir
    local log_file="$LOG_DIR/app.log"
    
    if [ -f "$log_file" ]; then
        echo -e "${GREEN}实时查看主日志文件: $log_file${NC}"
        echo -e "${BLUE}按 Ctrl+C 退出${NC}"
        tail -f "$log_file"
    else
        echo -e "${RED}错误: 主日志文件不存在${NC}"
        exit 1
    fi
}

# 实时查看错误日志
tail_error_log() {
    check_log_dir
    local log_file="$LOG_DIR/error.log"
    
    if [ -f "$log_file" ]; then
        echo -e "${GREEN}实时查看错误日志文件: $log_file${NC}"
        echo -e "${BLUE}按 Ctrl+C 退出${NC}"
        tail -f "$log_file"
    else
        echo -e "${RED}错误: 错误日志文件不存在${NC}"
        exit 1
    fi
}

# 显示最近的日志
show_log() {
    check_log_dir
    local lines=${1:-100}
    local log_file="$LOG_DIR/app.log"
    
    if [ -f "$log_file" ]; then
        echo -e "${GREEN}显示最近 $lines 行主日志:${NC}"
        tail -n "$lines" "$log_file"
    else
        echo -e "${RED}错误: 主日志文件不存在${NC}"
        exit 1
    fi
}

# 显示最近的错误日志
show_error_log() {
    check_log_dir
    local lines=${1:-50}
    local log_file="$LOG_DIR/error.log"
    
    if [ -f "$log_file" ]; then
        echo -e "${GREEN}显示最近 $lines 行错误日志:${NC}"
        tail -n "$lines" "$log_file"
    else
        echo -e "${RED}错误: 错误日志文件不存在${NC}"
        exit 1
    fi
}

# 显示日志文件大小
show_size() {
    check_log_dir
    echo -e "${GREEN}日志文件大小统计:${NC}"
    
    if [ -d "$LOG_DIR" ]; then
        find "$LOG_DIR" -name "*.log" -type f -exec ls -lh {} \; | while read -r line; do
            echo "  $line"
        done
        
        echo ""
        echo -e "${BLUE}总大小:${NC}"
        du -sh "$LOG_DIR"
    else
        echo -e "${RED}错误: 日志目录不存在${NC}"
    fi
}

# 清理旧日志文件
clean_logs() {
    check_log_dir
    local days=${1:-7}
    
    echo -e "${YELLOW}清理 $days 天前的日志文件...${NC}"
    
    # 查找并删除旧的轮转日志文件
    find "$LOG_DIR" -name "*.log.*" -type f -mtime +$days -print0 | while IFS= read -r -d '' file; do
        echo -e "${RED}删除: $file${NC}"
        rm -f "$file"
    done
    
    echo -e "${GREEN}清理完成${NC}"
}

# 手动轮转日志文件
rotate_logs() {
    check_log_dir
    echo -e "${YELLOW}手动轮转日志文件...${NC}"
    
    # 轮转主日志文件
    if [ -f "$LOG_DIR/app.log" ]; then
        mv "$LOG_DIR/app.log" "$LOG_DIR/app.$(date +%Y%m%d_%H%M%S).log"
        echo -e "${GREEN}主日志文件已轮转${NC}"
    fi
    
    # 轮转错误日志文件
    if [ -f "$LOG_DIR/error.log" ]; then
        mv "$LOG_DIR/error.log" "$LOG_DIR/error.$(date +%Y%m%d_%H%M%S).log"
        echo -e "${GREEN}错误日志文件已轮转${NC}"
    fi
    
    echo -e "${GREEN}轮转完成${NC}"
}

# 主逻辑
case "${1:-help}" in
    "tail")
        tail_log
        ;;
    "tail-error")
        tail_error_log
        ;;
    "show")
        show_log "$2"
        ;;
    "show-error")
        show_error_log "$2"
        ;;
    "size")
        show_size
        ;;
    "clean")
        clean_logs "$2"
        ;;
    "rotate")
        rotate_logs
        ;;
    "help"|*)
        show_help
        ;;
esac
