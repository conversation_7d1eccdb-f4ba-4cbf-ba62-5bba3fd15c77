#!/usr/bin/env node

/**
 * 测试环境变量加载
 */

// 加载环境变量
require('dotenv').config();
const { logger } = require('../utils/logger');

logger.info('🔍 环境变量测试');
logger.info('='.repeat(30));

// 测试关键环境变量
const testVars = [
    'MODERATION_ENABLED',
    'MODERATION_STRICTNESS',
    'MODERATION_LLM_API_KEY',
    'MODERATION_LLM_MODEL',
    'MODERATION_LLM_URL'
];

console.log('\n📋 环境变量状态:');
testVars.forEach(varName => {
    const value = process.env[varName];
    const status = value ? '✅ 已设置' : '❌ 未设置';
    console.log(`${varName}: ${status}`);
    
    if (value && !varName.includes('API_KEY')) {
        console.log(`   值: ${value}`);
    } else if (value && varName.includes('API_KEY')) {
        console.log(`   值: ${value.substring(0, 8)}...`);
    }
});

// 测试配置加载
console.log('\n🔧 配置加载测试:');
try {
    const { MODERATION_CONFIG } = require('../config/moderation');
    console.log('✅ 配置文件加载成功');
    console.log(`   审核启用: ${MODERATION_CONFIG.enabled}`);
    console.log(`   严格程度: ${MODERATION_CONFIG.strictness}`);
    console.log(`   API密钥: ${MODERATION_CONFIG.llm.apiKey ? '已配置' : '未配置'}`);
} catch (error) {
    console.log('❌ 配置文件加载失败:', error.message);
}

console.log('\n💡 建议:');
if (!process.env.MODERATION_LLM_API_KEY) {
    console.log('❌ 请创建.env文件并添加API密钥:');
    console.log('   echo "MODERATION_LLM_API_KEY=sk-your-key-here" > .env');
} else {
    console.log('✅ 环境变量配置正确，可以启动服务');
}
