#!/usr/bin/env node

/**
 * 多数据源测试脚本
 * 测试 fetchUpstreamStatus 函数的多数据源功能
 */

require('dotenv').config();
const { logger } = require('../utils/logger');
const fetch = require('node-fetch');
const https = require('https');

// 创建HTTPS Agent，取消证书验证
const httpsAgent = new https.Agent({
    rejectUnauthorized: false
});

/**
 * 从上游轮询URL获取最新状态
 * 支持多个数据源，优先从 godprivate.net，失败时使用 asyncdata.net
 * 包含重试机制处理网络问题和SSL错误
 */
async function fetchUpstreamStatus(taskId, maxRetries = 8, retryDelay = 1000) {
    // 定义多个数据源配置，按优先级排序
    const dataSources = [
        { name: 'godprivate.net', url: `http://godprivate.net:18081/source/${taskId}` },
        { name: 'asyncdata.net', url: `https://asyncdata.net/source/${taskId}` }
    ];

    let lastError;
    let currentSourceIndex = 0;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        // 选择当前尝试使用的数据源
        const currentSource = dataSources[currentSourceIndex];
        const { name: sourceName, url: currentUrl } = currentSource;

        try {
            logger.info(`[${attempt}/${maxRetries}] 尝试从 ${sourceName} 获取状态: ${currentUrl}`);

            const response = await fetch(currentUrl, {
                timeout: 30000,
                agent: currentUrl.startsWith('https:') ? httpsAgent : undefined
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            logger.info(`✅ 成功从 ${sourceName} 获取到状态数据`);
            return result;

        } catch (error) {
            lastError = error;
            logger.warn(`❌ [${attempt}/${maxRetries}] ${sourceName} 请求失败: ${error.message}`);

            // 检查是否是网络相关错误，包括SSL错误和更多网络问题
            const isNetworkError = error.message.includes('EAI_AGAIN') ||
                                 error.message.includes('ENOTFOUND') ||
                                 error.message.includes('ECONNRESET') ||
                                 error.message.includes('ETIMEDOUT') ||
                                 error.message.includes('EPROTO') ||
                                 error.message.includes('ECONNREFUSED') ||
                                 error.message.includes('EHOSTUNREACH') ||
                                 error.message.includes('ENETUNREACH') ||
                                 error.message.includes('EPIPE') ||
                                 error.message.includes('fetch failed') ||
                                 error.message.includes('timeout') ||
                                 error.message.includes('socket hang up') ||
                                 error.code === 'ENOTFOUND' ||
                                 error.code === 'ETIMEDOUT' ||
                                 error.code === 'ECONNRESET' ||
                                 error.code === 'ECONNREFUSED';

            // 检查是否是HTTP 5xx错误（服务器错误，可以重试）
            const isServerError = error.message.includes('HTTP 5') ||
                                 (error.response && error.response.status >= 500);

            // 如果还没达到最大重试次数，继续重试
            if (attempt < maxRetries) {
                // 对于网络错误或服务器错误，切换到下一个数据源
                if (isNetworkError || isServerError) {
                    const nextSourceIndex = (currentSourceIndex + 1) % dataSources.length;
                    const nextSourceName = dataSources[nextSourceIndex].name;

                    logger.info(`🔄 网络/服务器错误，切换数据源: ${sourceName} → ${nextSourceName}`);
                    currentSourceIndex = nextSourceIndex;

                    logger.warn(`⏳ ${retryDelay}ms 后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    // 指数退避：每次重试延迟时间翻倍，但最大不超过30秒
                    retryDelay = Math.min(retryDelay * 2, 30000);
                } else {
                    // 对于非网络错误（如404等），立即切换到下一个数据源
                    const nextSourceIndex = (currentSourceIndex + 1) % dataSources.length;
                    const nextSourceName = dataSources[nextSourceIndex].name;

                    logger.info(`🔄 非网络错误，立即切换数据源: ${sourceName} → ${nextSourceName}`);
                    currentSourceIndex = nextSourceIndex;
                }
                continue;
            }

            // 达到最大重试次数，跳出循环
            logger.error(`❌ 已达到最大重试次数 ${maxRetries}，停止尝试`);
            break;
        }
    }

    throw new Error(`Failed to fetch upstream status after ${maxRetries} attempts from all data sources. Last error from ${dataSources[currentSourceIndex].name}: ${lastError.message}`);
}

// 测试函数
async function testMultiDataSource() {
    logger.info('🧪 开始测试多数据源功能');
    logger.info('='.repeat(50));

    // 测试用的taskId（可能不存在，用于测试错误处理）
    const testTaskId = 'test-task-id-12345';

    try {
        logger.info(`📋 测试任务ID: ${testTaskId}`);
        logger.info('🔍 开始获取状态...');
        
        const result = await fetchUpstreamStatus(testTaskId, 4, 1000);
        
        logger.info('✅ 测试成功！');
        logger.info('📊 返回结果:', JSON.stringify(result, null, 2));
        
    } catch (error) {
        logger.error('❌ 测试失败:', error.message);
        logger.info('💡 这是预期的，因为测试用的taskId可能不存在');
        logger.info('🎯 重要的是观察是否尝试了多个数据源');
    }

    logger.info('\n📝 测试完成');
    logger.info('🔍 请检查日志中是否显示了多个数据源的尝试');
}

// 运行测试
testMultiDataSource().catch(error => {
    logger.error('测试脚本错误:', error);
    process.exit(1);
});
