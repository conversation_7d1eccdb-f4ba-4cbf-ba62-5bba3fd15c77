#!/usr/bin/env node

/**
 * 测试"is bound to a different event loop"错误的脚本
 * 模拟上游服务502错误和网络问题，验证事件监听器处理
 */

require('dotenv').config();
const fetch = require('node-fetch');
const { logger } = require('./utils/logger');

// 测试配置
const TEST_CONFIG = {
    baseUrl: 'http://localhost:8849',
    testCases: [
        {
            name: '正常请求测试',
            payload: {
                prompt: 'A beautiful sunset over the ocean',
                model: 'veo2'
            },
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-key'
            }
        },
        {
            name: '带图片的请求测试',
            payload: {
                prompt: 'A cat playing in the garden',
                model: 'veo2',
                images: ['https://example.com/test-image.jpg']
            },
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-key'
            }
        },
        {
            name: '无效Authorization测试',
            payload: {
                prompt: 'Test prompt',
                model: 'veo2'
            },
            headers: {
                'Content-Type': 'application/json'
                // 故意不包含Authorization
            }
        }
    ]
};

/**
 * 执行单个测试用例
 */
async function runTestCase(testCase, index) {
    logger.info(`\n=== 测试用例 ${index + 1}: ${testCase.name} ===`);
    
    try {
        const startTime = Date.now();
        
        const response = await fetch(`${TEST_CONFIG.baseUrl}/api/video/submit`, {
            method: 'POST',
            headers: testCase.headers,
            body: JSON.stringify(testCase.payload),
            timeout: 35000 // 35秒超时，比服务端的30秒稍长
        });
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        logger.info(`请求耗时: ${duration}ms`);
        logger.info(`响应状态: ${response.status} ${response.statusText}`);
        
        const responseText = await response.text();
        logger.info(`响应内容: ${responseText}`);
        
        // 尝试解析JSON
        try {
            const responseJson = JSON.parse(responseText);
            if (responseJson.success && responseJson.data && responseJson.data.taskId) {
                logger.info(`✅ 任务提交成功，TaskID: ${responseJson.data.taskId}`);
                return {
                    success: true,
                    taskId: responseJson.data.taskId,
                    duration
                };
            } else {
                logger.warn(`⚠️  任务提交失败: ${responseJson.error || '未知错误'}`);
                return {
                    success: false,
                    error: responseJson.error || '未知错误',
                    duration
                };
            }
        } catch (parseError) {
            logger.error(`❌ 响应解析失败: ${parseError.message}`);
            return {
                success: false,
                error: `响应解析失败: ${parseError.message}`,
                duration
            };
        }
        
    } catch (error) {
        logger.error(`❌ 请求失败: ${error.message}`);
        logger.error(`错误详情:`, {
            name: error.name,
            code: error.code,
            stack: error.stack
        });
        
        return {
            success: false,
            error: error.message,
            duration: 0
        };
    }
}

/**
 * 并发测试 - 模拟多个同时请求
 */
async function runConcurrentTest() {
    logger.info(`\n=== 并发测试 ===`);
    logger.info(`同时发送5个请求，测试事件监听器处理...`);
    
    const promises = [];
    for (let i = 0; i < 5; i++) {
        const testCase = {
            name: `并发请求-${i + 1}`,
            payload: {
                prompt: `Concurrent test request ${i + 1}`,
                model: 'veo2'
            },
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-key'
            }
        };
        
        promises.push(runTestCase(testCase, i));
    }
    
    try {
        const results = await Promise.allSettled(promises);
        
        let successCount = 0;
        let failureCount = 0;
        
        results.forEach((result, index) => {
            if (result.status === 'fulfilled' && result.value.success) {
                successCount++;
            } else {
                failureCount++;
                logger.error(`并发请求 ${index + 1} 失败:`, result.reason || result.value.error);
            }
        });
        
        logger.info(`\n并发测试结果: 成功 ${successCount}, 失败 ${failureCount}`);
        
    } catch (error) {
        logger.error(`并发测试异常: ${error.message}`);
    }
}

/**
 * 主测试函数
 */
async function main() {
    logger.info('🚀 开始测试事件循环绑定问题...');
    logger.info(`测试目标: ${TEST_CONFIG.baseUrl}`);
    
    // 首先检查服务是否可用
    try {
        const healthResponse = await fetch(`${TEST_CONFIG.baseUrl}/health`, { timeout: 5000 });
        if (!healthResponse.ok) {
            throw new Error(`Health check failed: ${healthResponse.status}`);
        }
        logger.info('✅ 服务健康检查通过');
    } catch (error) {
        logger.error(`❌ 服务不可用: ${error.message}`);
        logger.error('请确保服务正在运行在端口8849');
        process.exit(1);
    }
    
    // 执行单个测试用例
    const results = [];
    for (let i = 0; i < TEST_CONFIG.testCases.length; i++) {
        const result = await runTestCase(TEST_CONFIG.testCases[i], i);
        results.push(result);
        
        // 测试用例之间等待1秒
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 执行并发测试
    await runConcurrentTest();
    
    // 汇总结果
    logger.info(`\n=== 测试汇总 ===`);
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;
    
    logger.info(`单个测试: 成功 ${successCount}, 失败 ${failureCount}`);
    
    if (failureCount > 0) {
        logger.warn('⚠️  发现失败的测试用例，请检查日志');
        results.forEach((result, index) => {
            if (!result.success) {
                logger.error(`测试用例 ${index + 1} 失败: ${result.error}`);
            }
        });
    } else {
        logger.info('✅ 所有测试用例都通过了');
    }
    
    logger.info('🏁 测试完成');
}

// 运行测试
if (require.main === module) {
    main().catch(error => {
        logger.error('测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = { runTestCase, runConcurrentTest };
