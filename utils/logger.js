/**
 * 统一日志工具模块
 * 提供带时间戳的日志输出，支持不同级别的日志
 * 支持同时输出到控制台和文件
 *
 * 环境变量:
 * - LOG_LEVEL: 设置日志级别 (debug, info, warn, error)
 * - LOG_TIMEZONE: 设置时区 (默认: Asia/Shanghai)
 * - NODE_ENV: production 环境下禁用彩色输出
 * - LOG_TO_FILE: 是否写入文件 (默认: true)
 * - LOG_DIR: 日志文件目录 (默认: ./logs)
 * - LOG_MAX_SIZE: 单个日志文件最大大小 (默认: 50MB)
 * - LOG_MAX_FILES: 保留的日志文件数量 (默认: 10)
 *
 * 时区示例:
 * - Asia/Shanghai (中国)
 * - America/New_York (美国东部)
 * - Europe/London (英国)
 * - UTC (协调世界时)
 */

const fs = require('fs');
const path = require('path');

const LOG_LEVELS = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3
};

const LOG_COLORS = {
    DEBUG: '\x1b[36m', // 青色
    INFO: '\x1b[32m',  // 绿色
    WARN: '\x1b[33m',  // 黄色
    ERROR: '\x1b[31m', // 红色
    RESET: '\x1b[0m'   // 重置颜色
};

class Logger {
    constructor() {
        // 从环境变量获取日志级别，默认为 INFO
        this.logLevel = this.parseLogLevel(process.env.LOG_LEVEL || 'info');
        this.enableColors = process.env.NODE_ENV !== 'production';

        // 文件日志配置
        this.enableFileLogging = process.env.LOG_TO_FILE !== 'false';
        this.logDir = process.env.LOG_DIR || './logs';
        this.maxSize = this.parseSize(process.env.LOG_MAX_SIZE || '50MB');
        this.maxFiles = parseInt(process.env.LOG_MAX_FILES || '10');

        // 初始化文件日志
        if (this.enableFileLogging) {
            this.initFileLogging();
        }
    }

    /**
     * 解析文件大小字符串 (如 "50MB", "100KB")
     */
    parseSize(sizeStr) {
        const units = { KB: 1024, MB: 1024 * 1024, GB: 1024 * 1024 * 1024 };
        const match = sizeStr.match(/^(\d+)(KB|MB|GB)$/i);
        if (match) {
            return parseInt(match[1]) * units[match[2].toUpperCase()];
        }
        return parseInt(sizeStr) || 50 * 1024 * 1024; // 默认50MB
    }

    /**
     * 初始化文件日志
     */
    initFileLogging() {
        try {
            // 确保日志目录存在
            if (!fs.existsSync(this.logDir)) {
                fs.mkdirSync(this.logDir, { recursive: true });
            }

            // 设置日志文件路径
            this.currentLogFile = path.join(this.logDir, 'app.log');
            this.errorLogFile = path.join(this.logDir, 'error.log');

            // 测试写入权限
            this.testWritePermission();

            // 检查并轮转日志文件
            this.rotateLogIfNeeded(this.currentLogFile);
            this.rotateLogIfNeeded(this.errorLogFile);

        } catch (error) {
            console.error('Failed to initialize file logging:', error);
            this.enableFileLogging = false;
        }
    }

    /**
     * 测试写入权限
     */
    testWritePermission() {
        try {
            const testFile = path.join(this.logDir, '.write_test');
            fs.writeFileSync(testFile, 'test');
            fs.unlinkSync(testFile);
        } catch (error) {
            console.warn(`Warning: No write permission for log directory ${this.logDir}, disabling file logging`);
            this.enableFileLogging = false;
            throw error;
        }
    }

    /**
     * 检查并轮转日志文件
     */
    rotateLogIfNeeded(logFile) {
        try {
            if (fs.existsSync(logFile)) {
                const stats = fs.statSync(logFile);
                if (stats.size >= this.maxSize) {
                    this.rotateLogFile(logFile);
                }
            }
        } catch (error) {
            console.error(`Failed to check log file size: ${logFile}`, error);
        }
    }

    /**
     * 轮转日志文件
     */
    rotateLogFile(logFile) {
        try {
            const ext = path.extname(logFile);
            const basename = path.basename(logFile, ext);
            const dirname = path.dirname(logFile);

            // 移动现有的编号文件
            for (let i = this.maxFiles - 1; i >= 1; i--) {
                const oldFile = path.join(dirname, `${basename}.${i}${ext}`);
                const newFile = path.join(dirname, `${basename}.${i + 1}${ext}`);

                if (fs.existsSync(oldFile)) {
                    if (i === this.maxFiles - 1) {
                        fs.unlinkSync(oldFile); // 删除最老的文件
                    } else {
                        fs.renameSync(oldFile, newFile);
                    }
                }
            }

            // 将当前文件重命名为 .1
            const firstRotatedFile = path.join(dirname, `${basename}.1${ext}`);
            fs.renameSync(logFile, firstRotatedFile);

        } catch (error) {
            console.error(`Failed to rotate log file: ${logFile}`, error);
        }
    }

    /**
     * 解析日志级别字符串
     */
    parseLogLevel(level) {
        const upperLevel = level.toUpperCase();
        return LOG_LEVELS[upperLevel] !== undefined ? LOG_LEVELS[upperLevel] : LOG_LEVELS.INFO;
    }

    /**
     * 格式化时间戳
     * 支持时区配置，默认使用 Asia/Shanghai (中国时区)
     */
    formatTimestamp() {
        const timezone = process.env.LOG_TIMEZONE || 'Asia/Shanghai';
        const now = new Date();

        // 使用 toLocaleString 获取指定时区的时间
        const options = {
            timeZone: timezone,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        };

        const localTime = now.toLocaleString('zh-CN', options);
        const milliseconds = String(now.getMilliseconds()).padStart(3, '0');

        // 格式化为 YYYY-MM-DD HH:mm:ss.SSS
        const [datePart, timePart] = localTime.split(' ');
        const formattedDate = datePart.replace(/\//g, '-');

        return `${formattedDate} ${timePart}.${milliseconds}`;
    }

    /**
     * 格式化日志消息
     */
    formatMessage(level, message, ...args) {
        const timestamp = this.formatTimestamp();
        const levelStr = level.padEnd(5);
        
        // 构建基础消息
        let formattedMessage = `[${timestamp}] [${levelStr}] ${message}`;
        
        // 如果有额外参数，添加到消息中
        if (args.length > 0) {
            const argsStr = args.map(arg => {
                if (typeof arg === 'object') {
                    try {
                        return JSON.stringify(arg, null, 2);
                    } catch (e) {
                        return String(arg);
                    }
                }
                return String(arg);
            }).join(' ');
            formattedMessage += ` ${argsStr}`;
        }

        return formattedMessage;
    }

    /**
     * 写入文件日志
     */
    writeToFile(level, formattedMessage) {
        if (!this.enableFileLogging) {
            return;
        }

        try {
            // 检查并轮转日志文件
            this.rotateLogIfNeeded(this.currentLogFile);

            // 写入主日志文件
            fs.appendFileSync(this.currentLogFile, formattedMessage + '\n');

            // 错误级别的日志同时写入错误日志文件
            if (level === 'ERROR') {
                this.rotateLogIfNeeded(this.errorLogFile);
                fs.appendFileSync(this.errorLogFile, formattedMessage + '\n');
            }
        } catch (error) {
            // 权限错误时禁用文件日志，避免重复错误
            if (error.code === 'EACCES' || error.code === 'EPERM') {
                console.warn(`Warning: Disabling file logging due to permission error: ${error.message}`);
                this.enableFileLogging = false;
            } else {
                console.error('Failed to write log to file:', error);
            }
        }
    }

    /**
     * 输出日志
     */
    log(level, levelNum, message, ...args) {
        if (levelNum < this.logLevel) {
            return;
        }

        const formattedMessage = this.formatMessage(level, message, ...args);

        // 输出到控制台
        if (this.enableColors) {
            const color = LOG_COLORS[level] || LOG_COLORS.RESET;
            console.log(`${color}${formattedMessage}${LOG_COLORS.RESET}`);
        } else {
            console.log(formattedMessage);
        }

        // 写入文件
        this.writeToFile(level, formattedMessage);
    }

    /**
     * DEBUG 级别日志
     */
    debug(message, ...args) {
        this.log('DEBUG', LOG_LEVELS.DEBUG, message, ...args);
    }

    /**
     * INFO 级别日志
     */
    info(message, ...args) {
        this.log('INFO', LOG_LEVELS.INFO, message, ...args);
    }

    /**
     * WARN 级别日志
     */
    warn(message, ...args) {
        this.log('WARN', LOG_LEVELS.WARN, message, ...args);
    }

    /**
     * ERROR 级别日志
     */
    error(message, ...args) {
        this.log('ERROR', LOG_LEVELS.ERROR, message, ...args);
    }

    /**
     * 设置日志级别
     */
    setLogLevel(level) {
        this.logLevel = this.parseLogLevel(level);
    }

    /**
     * 获取当前日志级别
     */
    getLogLevel() {
        const levels = Object.keys(LOG_LEVELS);
        return levels.find(level => LOG_LEVELS[level] === this.logLevel) || 'INFO';
    }

    /**
     * 创建带前缀的子日志器
     */
    createChild(prefix) {
        return {
            debug: (message, ...args) => this.debug(`[${prefix}] ${message}`, ...args),
            info: (message, ...args) => this.info(`[${prefix}] ${message}`, ...args),
            warn: (message, ...args) => this.warn(`[${prefix}] ${message}`, ...args),
            error: (message, ...args) => this.error(`[${prefix}] ${message}`, ...args)
        };
    }
}

// 创建全局日志器实例
const logger = new Logger();

// 导出日志器实例和类
module.exports = {
    logger,
    Logger,
    LOG_LEVELS
};
